create table cipher_token
(
    id                  UUID         NOT NULL PRIMARY KEY,
    cipher_access_token VARCHAR NOT NULL,
    scope               VARCHAR(255),
    expire_date         TIMES<PERSON>MP,
    token_type          VARCHAR(50),
    created_at          TIMESTAMP    NOT NULL DEFAULT NOW(),
    created_by          <PERSON><PERSON><PERSON><PERSON>(100),
    updated_at          TIMESTAMP    NOT NULL DEFAULT NOW(),
    updated_by          <PERSON><PERSON><PERSON><PERSON>(100),
    deleted_at          TIMESTAMP,
    deleted_by          VA<PERSON><PERSON>R(100)
);

COMMENT ON COLUMN cipher_token.id IS 'Primary key for the cipher token.';
COMMENT ON COLUMN cipher_token.cipher_access_token IS 'Access token used for authentication';
COMMENT ON COLUMN cipher_token.scope IS 'Token scope defining access privileges';
COMMENT ON COLUMN cipher_token.expire_date IS 'Expiration date of the token';
COMMENT ON COLUMN cipher_token.token_type IS 'Type of the token, e.g., Bearer';
