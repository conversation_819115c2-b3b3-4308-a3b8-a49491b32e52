package com.mercaso.wms.delivery.infrastructure.repository.gps.jpa.mapper;

import com.mercaso.wms.delivery.domain.gps.GpsRaw;
import com.mercaso.wms.delivery.infrastructure.repository.gps.jpa.dataobject.GpsRawDo;
import com.mercaso.wms.infrastructure.repository.BaseDoMapper;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GpsRawDoMapper extends BaseDoMapper<GpsRawDo, GpsRaw> {

} 