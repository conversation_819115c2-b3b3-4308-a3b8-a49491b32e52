package com.mercaso.wms.application.dto.receivingtask;

import com.mercaso.wms.application.dto.BaseDto;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ReceivingTaskItemDto extends BaseDto {

    private UUID id;

    private UUID receivingTaskId;

    private String orderNumber;

    private Integer line;

    private UUID batchItemId;

    private UUID itemId;

    private String department;

    private String category;

    private String skuNumber;

    private String title;

    private String locationName;

    private UUID locationId;

    private String aisleNumber;

    private Integer receivingSequence;

    private Integer expectQty;

    private Integer receivedQty;

    private String errorInfo;

    private String breakdownName;

    private UUID shippingOrderId;

    private UUID shippingOrderItemId;

}
