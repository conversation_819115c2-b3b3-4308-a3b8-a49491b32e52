package com.mercaso.wms.domain.vendoritem.enums;

import lombok.Getter;

@Getter
public enum VendorItemStatus {

    ACTIVE("active"),
    INACTIVE("inactive");

    private final String value;

    VendorItemStatus(String value) {
        this.value = value;
    }

    public static VendorItemStatus fromValue(String value) {
        for (VendorItemStatus status : VendorItemStatus.values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        return ACTIVE;
    }

}
