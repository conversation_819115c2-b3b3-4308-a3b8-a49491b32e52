package com.mercaso.wms.delivery.application.query;

import com.mercaso.wms.application.query.SortType;
import com.mercaso.wms.delivery.domain.deliverytask.enums.DeliveryTaskStatus;
import jakarta.validation.constraints.Min;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryTaskQuery {

    private List<String> taskNumbers;
    private List<DeliveryTaskStatus> statuses;
    private LocalDate deliveryDate;
    private List<String> driverNames;
    private List<String> truckNumbers;
    private List<SortType> sortTypes;
    private List<UUID> driverUserIds;
    private Boolean hasRescheduledOrders;

    @Min(value = 1, message = "Page number must be greater than 0")
    private int page = 1;

    @Min(value = 1, message = "Page size must be greater than 0")
    private int pageSize = 10;
} 