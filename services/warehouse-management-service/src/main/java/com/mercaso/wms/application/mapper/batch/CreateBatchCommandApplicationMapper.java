package com.mercaso.wms.application.mapper.batch;

import static org.mapstruct.NullValuePropertyMappingStrategy.IGNORE;

import com.mercaso.wms.application.command.batch.CreateBatchCommand;
import com.mercaso.wms.application.mapper.BaseCommandApplicationMapper;
import com.mercaso.wms.domain.batch.Batch;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = IGNORE, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CreateBatchCommandApplicationMapper extends BaseCommandApplicationMapper<CreateBatchCommand, Batch> {

    CreateBatchCommandApplicationMapper INSTANCE = Mappers.getMapper(CreateBatchCommandApplicationMapper.class);

}
