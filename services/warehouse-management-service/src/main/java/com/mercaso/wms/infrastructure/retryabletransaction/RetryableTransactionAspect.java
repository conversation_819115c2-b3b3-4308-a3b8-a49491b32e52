package com.mercaso.wms.infrastructure.retryabletransaction;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.backoff.ExponentialRandomBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;

@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class RetryableTransactionAspect {

    private final ThreadLocal<Throwable> exceptionContext = new ThreadLocal<>();
    private final PlatformTransactionManager transactionManager;

    @Around(value = "execution(@com.mercaso.wms.infrastructure.retryabletransaction.RetryableTransaction * *(..))")
    public Object executeJoinPoint(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            RetryableTransaction annotation = method.getAnnotation(RetryableTransaction.class);
            int maxAttempts = annotation.maxAttempts();

            TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);

            if (annotation.propagation() != null) {
                transactionTemplate.setPropagationBehavior(annotation.propagation().value());
            }

            RetryTemplate retryTemplate = buildTemplate(annotation);

            return retryTemplate.execute(buildRetryCallBack(joinPoint, method, transactionTemplate), retryContext -> {
                if (retryContext.getRetryCount() >= maxAttempts) {
                    log.warn("[RetryableTransaction] the method {} retry number {} has been exhausted.",
                        method.getName(),
                        maxAttempts);
                }
                throw (Exception) exceptionContext.get();
            });
        } finally {
            exceptionContext.remove();
        }
    }

    private RetryCallback<Object, Throwable> buildRetryCallBack(ProceedingJoinPoint joinPoint,
        Method method,
        TransactionTemplate transactionTemplate) {
        return context -> {
            try {
                return transactionTemplate.execute(status -> {
                    Object result;
                    try {
                        result = joinPoint.proceed();
                    } catch (Throwable throwable) {
                        log.warn("[RetryableTransaction] process failure, retry {} for method {}",
                            context.getRetryCount(),
                            method.getName());
                        throw new RetryableTransactionException(throwable.getMessage(), throwable);
                    }
                    return result;
                });
            } catch (RetryableTransactionException e) {
                exceptionContext.set(e.getCause());
                throw e.getCause();
            }
        };
    }

    private RetryTemplate buildTemplate(RetryableTransaction retryableTransactional) {
        RetryTemplate template = new RetryTemplate();
        ExponentialRandomBackOffPolicy backOffPolicy = new ExponentialRandomBackOffPolicy();

        Assert.isTrue(retryableTransactional.retryFor().length <= 16,
            "The exception type retried for is gather than 16, please adjust this size.");

        Map<Class<? extends Throwable>, Boolean> retryableExceptions = HashMap.newHashMap(16);
        for (Class<? extends Throwable> retryException : retryableTransactional.retryFor()) {
            retryableExceptions.put(retryException, true);
        }
        SimpleRetryPolicy policy = new SimpleRetryPolicy(retryableTransactional.maxAttempts(), retryableExceptions);
        template.setRetryPolicy(policy);
        template.setBackOffPolicy(backOffPolicy);
        return template;
    }

}
