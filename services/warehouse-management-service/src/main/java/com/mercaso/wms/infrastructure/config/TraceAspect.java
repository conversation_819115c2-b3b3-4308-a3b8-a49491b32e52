package com.mercaso.wms.infrastructure.config;

import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.Tracer;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

@Slf4j
@Aspect
@Component
@AllArgsConstructor
public class TraceAspect {

    private final Tracer tracer;

    @Around("execution(* com.mercaso.wms.interfaces.*(..))"
        + "&& !execution(* initBinder(..))"
        + "&& !execution(* java.lang.Object.*(..))")
    public Object traceMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        final Signature signature = joinPoint.getSignature();
        final String className = StringUtils.substringAfterLast(signature.getDeclaringTypeName(), ".");
        final String operationName = className + ":" + signature.getName();

        Span span = tracer.spanBuilder(operationName).startSpan();
        span.setAttribute("class", className);
        try {
            return joinPoint.proceed();
        } catch (Exception ex) {
            span.recordException(ex);
            throw ex;
        } finally {
            span.end();
        }
    }

}
