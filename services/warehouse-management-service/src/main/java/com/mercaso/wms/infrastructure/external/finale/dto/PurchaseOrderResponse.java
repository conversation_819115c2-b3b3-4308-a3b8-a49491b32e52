package com.mercaso.wms.infrastructure.external.finale.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PurchaseOrderResponse {

    private String orderId;
    private String orderUrl;
    private String orderTypeId;
    private String orderHistoryListUrl;
    private String actionUrlLock;
    private String actionUrlComplete;
    private String actionUrlCancel;
    private String reserveAllUrl;
    private Integer orderItemListTotal;
    private String statusId;
    private String destinationFacilityUrl;
    private List<OrderItem> orderItemList;
    private List<StatusHistory> statusIdHistoryList;

    @Data
    public static class OrderItem {
        private String orderItemUrl;
        private String reserveUrl;
        private String productId;
        private String productUrl;
        private Integer quantity;
    }

    @Data
    public static class StatusHistory {
        private String statusId;
        private String userLoginUrl;
    }
}