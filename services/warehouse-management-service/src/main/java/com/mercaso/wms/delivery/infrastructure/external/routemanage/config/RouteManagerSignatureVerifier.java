package com.mercaso.wms.delivery.infrastructure.external.routemanage.config;

import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBadRequestException;
import jakarta.servlet.http.HttpServletRequest;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.NoSuchElementException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.coyote.BadRequestException;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class RouteManagerSignatureVerifier {

    private static final String SIGNATURE_PARAM_NAME = "signature";
    private final RouteManagerSignatureCalculator routeManagerSignatureCalculator;

    /**
     * Verifies the signature in the HTTP request.
     * Extracts the signature from the URL, computes the expected signature,
     * and compares them to validate the request.
     *
     * @param request The HTTP request containing the signature
     * @throws DeliveryBadRequestException if the signature is invalid or missing
     */
    public void verifySignature(HttpServletRequest request) {
        try {
            log.debug("Starting signature verification for request: {}", request.getRequestURL());
            String inboundRequestUrl = constructInboundUrl(request);
            log.debug("Constructed inbound URL: {}", inboundRequestUrl);

            QueryData queryData;
            try {
                queryData = getQueryData(inboundRequestUrl);
                log.debug("Query data extracted: signature present, stripped URL: {}", queryData.strippedUrl());
            } catch (BadRequestException e) {
                log.warn("Failed to extract query data: {}", e.getMessage());
                throw new DeliveryBadRequestException(e.getMessage());
            }

            String signatureFromUrl = queryData.signatureValue();
            String inboundRequestUrlWithoutSignature = queryData.strippedUrl();

            log.debug("Computing signature for URL: {}", inboundRequestUrlWithoutSignature);
            String computedSignature = routeManagerSignatureCalculator.computeSignature(inboundRequestUrlWithoutSignature);

            if (!computedSignature.equals(signatureFromUrl)) {
                log.warn("Signature verification failed. Expected: {}, Actual: {}", computedSignature, signatureFromUrl);
                throw new DeliveryBadRequestException("Invalid signature");
            }

            log.info("Signature verification successful");
        } catch (URISyntaxException e) {
            log.error("URI syntax exception while verifying signature", e);
            throw new DeliveryBadRequestException("Invalid signature format");
        }
    }

    /**
     * Extracts query data from the URL, including the signature parameter and URL without signature.
     *
     * @param url The complete URL including query parameters
     * @return QueryData containing the parsed parameters, signature value, and URL without signature
     * @throws URISyntaxException if the URL is malformed
     * @throws BadRequestException if the signature is missing or query parameters are absent
     */
    private QueryData getQueryData(String url) throws URISyntaxException, NoSuchElementException, BadRequestException {
        URI uri = new URI(url);
        String query = uri.getRawQuery();

        if (query == null || query.isEmpty()) {
            log.warn("No query parameters found in URL: {}", url);
            throw new BadRequestException("No query parameters");
        }

        // Parse query parameters while maintaining order
        List<NameValuePair> pairs = parseQueryParams(query);
        log.debug("Parsed {} query parameters", pairs.size());

        // Find signature parameter
        String signature = null;
        List<NameValuePair> paramsWithoutSignature = new ArrayList<>();

        for (NameValuePair pair : pairs) {
            if (SIGNATURE_PARAM_NAME.equals(pair.name())) {
                signature = pair.value();
                log.debug("Found signature parameter: {}", maskSignature(signature));
            } else {
                paramsWithoutSignature.add(pair);
            }
        }

        if (signature == null) {
            log.warn("No signature parameter found in URL: {}", url);
            throw new BadRequestException("No signature");
        }

        // Build new query string without signature
        String newQuery = buildQueryString(paramsWithoutSignature);

        // Create new URI without signature
        URI newUri = new URI(uri.getScheme(),
            uri.getAuthority(),
            uri.getPath(),
            newQuery.isEmpty() ? null : newQuery,
            uri.getFragment());

        String strippedUrl = newUri.toString();
        log.debug("Created URL without signature: {}", strippedUrl);

        return new QueryData(pairs, signature, strippedUrl);
    }

    /**
     * Builds a query string from a list of name-value pairs.
     *
     * @param params List of parameters to include in the query string
     * @return Formatted query string
     */
    private String buildQueryString(List<NameValuePair> params) {
        if (params.isEmpty()) {
            return "";
        }

        return params.stream()
            .map(pair -> String.join("=",
                URLEncoder.encode(pair.name(), StandardCharsets.UTF_8),
                URLEncoder.encode(pair.value(), StandardCharsets.UTF_8)))
            .reduce((p1, p2) -> p1 + "&" + p2)
            .orElse("");
    }

    /**
     * Parses query parameters from a query string while maintaining original order.
     *
     * @param query The query string part of a URL
     * @return List of name-value pairs
     */
    private List<NameValuePair> parseQueryParams(String query) {
        List<NameValuePair> result = new ArrayList<>();
        String[] pairs = query.split("&");
        log.debug("Parsing {} query parameter pairs", pairs.length);

        for (String pair : pairs) {
            int idx = pair.indexOf("=");
            if (idx > 0) {
                // Normal case: "name=value"
                String name = URLDecoder.decode(pair.substring(0, idx), StandardCharsets.UTF_8);
                String value = URLDecoder.decode(pair.substring(idx + 1), StandardCharsets.UTF_8);
                result.add(new NameValuePair(name, value));
            } else if (idx == 0) {
                // Edge case: "=value"
                String value = URLDecoder.decode(pair.substring(1), StandardCharsets.UTF_8);
                result.add(new NameValuePair("", value));
            } else {
                // Edge case: "name" without "="
                String name = URLDecoder.decode(pair, StandardCharsets.UTF_8);
                result.add(new NameValuePair(name, ""));
            }
        }

        log.debug("Successfully parsed {} name-value pairs", result.size());
        return result;
    }

    /**
     * Constructs the full inbound URL from the HTTP request.
     *
     * @param request The HTTP request
     * @return The full URL including query string
     */
    private String constructInboundUrl(HttpServletRequest request) {
        StringBuilder requestURL = new StringBuilder(request.getRequestURL());
        String queryString = request.getQueryString();
        return "%s?%s".formatted(requestURL, queryString);
    }

    /**
     * Masks a signature value for logging purposes to avoid exposing sensitive data.
     *
     * @param signature The signature to mask
     * @return Masked signature (first 4 characters followed by "...")
     */
    private String maskSignature(String signature) {
        if (signature == null || signature.length() <= 4) {
            return "[protected]";
        }
        return signature.substring(0, 4) + "...";
    }

    private record QueryData(List<NameValuePair> queryParams, String signatureValue, String strippedUrl) {

    }

    /**
     * Simple implementation to replace Apache's NameValuePair
     */
    private record NameValuePair(String name, String value) {

    }
}
