package com.mercaso.ims.domain.itemversion;

import com.mercaso.ims.domain.BaseDomainRepository;
import java.util.List;
import java.util.UUID;

public interface ItemVersionRepository extends BaseDomainRepository<ItemVersion, UUID> {

    ItemVersion findBySkuAndVersion(String skuNumber, Integer version);

    ItemVersion findByItemIdAndVersion(UUID itemId, Integer version);

    List<ItemVersion> findByItemId(UUID itemId);

}
