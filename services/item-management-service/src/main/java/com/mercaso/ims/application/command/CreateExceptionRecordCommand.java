package com.mercaso.ims.application.command;

import com.mercaso.ims.application.BaseCommand;
import com.mercaso.ims.domain.exceptionrecord.enums.EntityType;
import com.mercaso.ims.domain.exceptionrecord.enums.ExceptionRecordStatus;
import com.mercaso.ims.domain.exceptionrecord.enums.ExceptionRecordType;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class CreateExceptionRecordCommand extends BaseCommand {

    private UUID businessEventId;
    private UUID entityId;
    private EntityType entityType;
    private ExceptionRecordStatus status;
    private ExceptionRecordType exceptionType;
    private String description;
}
