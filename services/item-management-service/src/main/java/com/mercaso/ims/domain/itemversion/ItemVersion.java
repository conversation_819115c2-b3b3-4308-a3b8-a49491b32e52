package com.mercaso.ims.domain.itemversion;

import com.mercaso.ims.domain.AggregateRoot;
import com.mercaso.ims.domain.BaseDomain;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
@Setter
@ToString
@SuperBuilder
public class ItemVersion extends BaseDomain implements AggregateRoot {

    private final UUID id;

    private UUID itemId;

    private String skuNumber;

    private String itemData;

    private Integer versionNumber;
}
