package com.mercaso.ims.infrastructure.event.applicationevent.listener;

import com.mercaso.ims.application.command.UpdateItemCommand;
import com.mercaso.ims.application.dto.CategoryDto;
import com.mercaso.ims.application.dto.CategoryNameDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.event.CategoryCreatedApplicationEvent;
import com.mercaso.ims.application.dto.event.CategoryDeletedApplicationEvent;
import com.mercaso.ims.application.dto.event.CategoryUpdatedApplicationEvent;
import com.mercaso.ims.application.dto.payload.CategoryUpdatedPayloadDto;
import com.mercaso.ims.application.queryservice.CategoryQueryApplicationService;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.service.ItemApplicationService;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class CategoryApplicationEventListener {

    private final CategoryQueryApplicationService categoryQueryApplicationService;
    private final ItemApplicationService itemApplicationService;
    private final ItemQueryApplicationService itemQueryApplicationService;
    private final Executor taskExecutor;

    @EventListener
    public void handleCategoryCreatedApplicationEvent(CategoryCreatedApplicationEvent categoryCreatedApplicationEvent) {

        log.info("handleCategoryCreatedApplicationEvent for event payload ={}", categoryCreatedApplicationEvent);

    }

    @EventListener
    public void handleCategoryUpdatedApplicationEvent(CategoryUpdatedApplicationEvent categoryUpdatedApplicationEvent) {

        log.info("handleCategoryUpdatedApplicationEvent for event payload ={}", categoryUpdatedApplicationEvent);

        CategoryUpdatedPayloadDto payload = categoryUpdatedApplicationEvent.getPayload();

        // Update items in leaf categories
        CompletableFuture.runAsync(() -> updateItemsInLeafCategories(payload.getCategoryId()), taskExecutor);
    }

    @EventListener
    public void handleCategoryDeletedApplicationEvent(CategoryDeletedApplicationEvent categoryDeletedApplicationEvent) {

        log.info("handleCategoryDeletedApplicationEvent for event payload ={}", categoryDeletedApplicationEvent);
    }


    private void updateItemsInLeafCategories(UUID categoryId) {
        log.info("updateItemsInLeafCategories for categoryId={}", categoryId);
        List<CategoryDto> leafCategories = categoryQueryApplicationService.findLeafCategory(categoryId);

        for (CategoryDto leafCategory : leafCategories) {
            log.info("updateItemsInLeafCategories for leafCategory={}", leafCategory);

            UUID leafCategoryId = leafCategory.getCategoryId();
            List<ItemDto> items = itemQueryApplicationService.findByCategoryId(leafCategoryId);

            log.info("updateItemsInLeafCategories for items={}", items);

            if (items.isEmpty()) {
                continue;
            }

            CategoryNameDto categoryNameDto = extractCategoryHierarchy(leafCategory);
            
            items.forEach(item -> updateItem(item, leafCategoryId, categoryNameDto));
        }
    }
    
    private CategoryNameDto extractCategoryHierarchy(CategoryDto leafCategory) {
        CategoryNameDto categoryNameDto = new CategoryNameDto();
        categoryNameDto.setClazz(leafCategory.getCategoryName());
        
        List<CategoryDto> ancestors = categoryQueryApplicationService.findAncestorNameByLeafCategory(
            leafCategory.getCategoryId());
            
        for (CategoryDto ancestor : ancestors) {
            switch (ancestor.getDepth()) {
                case 3: categoryNameDto.setDepartment(ancestor.getCategoryName()); break;
                case 2: categoryNameDto.setCategory(ancestor.getCategoryName()); break;
                case 1: categoryNameDto.setSubCategory(ancestor.getCategoryName()); break;
            }
        }
        
        return categoryNameDto;
    }
    
    private void updateItem(ItemDto item, UUID categoryId, CategoryNameDto categoryNameDto) {
        itemApplicationService.update(UpdateItemCommand.builder()
            .id(item.getId())
            .categoryId(categoryId)
            .department(categoryNameDto.getDepartment())
            .category(categoryNameDto.getCategory())
            .subCategory(categoryNameDto.getSubCategory())
            .clazz(categoryNameDto.getClazz())
            .build());
    }

}
