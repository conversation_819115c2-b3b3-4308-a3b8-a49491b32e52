package com.mercaso.ims.interfaces.rest.search;

import static com.mercaso.ims.application.query.ItemQuery.getCustomFilterValue;

import com.mercaso.ims.application.dto.ItemListCustomFilterKeyDto;
import com.mercaso.ims.application.dto.ItemListDto;
import com.mercaso.ims.application.query.ItemQuery;
import com.mercaso.ims.application.query.ItemQuery.SortType;
import com.mercaso.ims.application.searchservice.ItemSearchApplicationService;
import jakarta.validation.constraints.Min;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/v2/search/items", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
@Validated
public class SearchItemV2RestApi {
    private final ItemSearchApplicationService itemSearchApplicationService;

    @GetMapping
    @PreAuthorize("hasAuthority('ims:read:items')")
    public ItemListDto searchItems(
        @RequestParam(value = "page", defaultValue = "1") @Min(value = 1, message = "Page number must be greater than 0") int page,
        @RequestParam(value = "pageSize", defaultValue = "20") @Min(value = 1, message = "Page size must be greater than 0") int pageSize,
        @RequestParam(value = "sort", required = false) List<SortType> sorts,
        @RequestParam(value = "customFilter", required = false) String customFilter) {
        log.info("[searchItems] param customFilter: {}.", customFilter);
        return itemSearchApplicationService.searchItemListV2(ItemQuery.builder()
            .page(page)
            .pageSize(pageSize)
            .customFilter(getCustomFilterValue(customFilter))
            .sorts(sorts)
            .build());
    }

    @GetMapping("/custom-filter-key")
    @PreAuthorize("hasAuthority('ims:read:items')")
    public List<ItemListCustomFilterKeyDto> searchItemsListCustomFilter() {
        return itemSearchApplicationService.searchItemsListCustomFilter();
    }
}
