package com.mercaso.ims.domain.itemcostchangerequest;

import com.mercaso.ims.application.command.CreateItemCostChangeRequestCommand;

public class ItemCostChangeRequestFactory {

    private ItemCostChangeRequestFactory() {
    }

    public static ItemCostChangeRequest createItemCostChangeRequest(CreateItemCostChangeRequestCommand command) {
        return ItemCostChangeRequest.builder()
            .vendorId(command.getVendorId())
            .vendorSkuNumber(command.getVendorSkuNumber())
            .vendorItemName(command.getVendorItemName())
            .skuNumber(command.getSkuNumber())
            .itemId(command.getItemId())
            .itemCostCollectionId(command.getItemCostCollectionId())
            .previousCost(command.getPreviousCost())
            .targetCost(command.getTargetCost())
            .tax(command.getTax())
            .crv(command.getCrv())
            .matchType(command.getMatchType())
            .status(command.getStatus())
            .vendorItemUpc(command.getVendorItemUpc())
            .availability(command.getAvailability())
            .costType(command.getCostType())
            .aisle(command.getAisle())
            .build();

    }


}
