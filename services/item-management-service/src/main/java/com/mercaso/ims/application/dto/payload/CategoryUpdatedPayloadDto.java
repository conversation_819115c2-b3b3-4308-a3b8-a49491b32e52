package com.mercaso.ims.application.dto.payload;

import com.mercaso.ims.application.dto.AmendDto;
import com.mercaso.ims.application.dto.CategoryDto;
import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CategoryUpdatedPayloadDto extends BusinessEventPayloadDto<AmendDto<CategoryDto>> {

    private UUID categoryId;

    private CategoryDto previous;

    private CategoryDto current;

    @Builder
    public CategoryUpdatedPayloadDto(CategoryDto previous, CategoryDto current, UUID categoryId) {
        super(new AmendDto<>(previous, current));
        this.categoryId = categoryId;
    }
}