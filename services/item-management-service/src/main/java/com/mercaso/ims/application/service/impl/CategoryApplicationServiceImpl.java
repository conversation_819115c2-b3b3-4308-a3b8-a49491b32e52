package com.mercaso.ims.application.service.impl;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.CATEGORY_HAS_ITEMS;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.CATEGORY_NAME_ALREADY_EXISTS;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.CATEGORY_NOT_FOUND;

import com.mercaso.ims.application.command.CreateCategoryCommand;
import com.mercaso.ims.application.command.CreateCategoryHierarchCommand;
import com.mercaso.ims.application.command.UpdateCategoryCommand;
import com.mercaso.ims.application.command.UpdatedCategoryHierarchCommand;
import com.mercaso.ims.application.dto.CategoryDto;
import com.mercaso.ims.application.dto.CategoryItemCountsDto;
import com.mercaso.ims.application.dto.CategoryTreeDto;
import com.mercaso.ims.application.dto.ItemCountsDto;
import com.mercaso.ims.application.dto.payload.CategoryCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.CategoryUpdatedPayloadDto;
import com.mercaso.ims.application.query.CategoryQuery;
import com.mercaso.ims.application.searchservice.ItemSearchApplicationService;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.CategoryHierarchyApplicationService;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.category.Category;
import com.mercaso.ims.domain.category.CategoryFactory;
import com.mercaso.ims.domain.category.enums.CategoryStatus;
import com.mercaso.ims.domain.category.service.CategoryService;
import com.mercaso.ims.domain.categoryhierarchy.CategoryHierarchy;
import com.mercaso.ims.domain.categoryhierarchy.service.CategoryHierarchyService;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.external.shopify.ShopifyAdaptor;
import com.mercaso.ims.infrastructure.repository.category.jpa.CustomizedCategoryJpaDao;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class CategoryApplicationServiceImpl implements CategoryApplicationService {

    private final CustomizedCategoryJpaDao customizedCategoryJpaDao;

    private final CategoryService categoryService;

    private final BusinessEventService businessEventService;

    private final CategoryHierarchyApplicationService categoryHierarchyApplicationService;

    private final ItemRepository itemRepository;

    private final CategoryHierarchyService categoryHierarchyService;

    private final ShopifyAdaptor shopifyAdaptor;

    private final ItemSearchApplicationService itemSearchApplicationService;

    public static final int DEPTH_TO_CLASS = 0;
    public static final int DEPTH_TO_PARENT = 1;
    public static final int DEPTH_TO_GRANDPARENT = 2;
    public static final int DEPTH_TO_GREAT_GRANDPARENT = 3;

    @Override
    public List<CategoryTreeDto> searchCategoryTree() {
        List<CategoryDto> allCategories = customizedCategoryJpaDao.getCategories(CategoryQuery.builder()
            .status(CategoryStatus.ACTIVE)
            .build());
        return buildCategoryTree(allCategories);
    }

    @Override
    public CategoryDto createCategory(CreateCategoryCommand command) {
        log.info("[createCategory] param command: {}.", command);

        checkAncestorCategoryIdValid(command.getAncestorCategoryId());

        validateCategoryNameUniqueness(command.getCategoryName(), command.getAncestorCategoryId());

        Category category = categoryService.save(CategoryFactory.create(command));

        categoryHierarchyApplicationService.createCategoryHierarchy(CreateCategoryHierarchCommand.builder()
                .ancestorCategoryId(command.getAncestorCategoryId())
                .categoryId(category.getId())
                .sortOrder(command.getSortOrder()).build());

        CategoryDto currentCategory = customizedCategoryJpaDao.getCategories(CategoryQuery.builder()
                .status(CategoryStatus.ACTIVE)
                .categoryId(category.getId())
                .depth(1)
                .build()).getFirst();

        businessEventService.dispatch(CategoryCreatedPayloadDto.builder().categoryId(category.getId()).data(currentCategory).build());
        return currentCategory;
    }

    @Override
    public void updateCategory(UUID categoryId, UpdateCategoryCommand command) {
        log.info("[updateCategory] param, categoryId:{}, command: {}.", categoryId, command);

        Category category = categoryService.findById(categoryId);

        if (null == category) {
            log.info("[updateCategory] Category not found, categoryId: {}.", categoryId);
            throw new ImsBusinessException(CATEGORY_NOT_FOUND);
        }

        CategoryDto previousCategory = customizedCategoryJpaDao.getCategories(CategoryQuery.builder()
                .status(CategoryStatus.ACTIVE)
                .categoryId(categoryId)
                .depth(1)
                .build()).getFirst();

        checkAncestorCategoryIdValid(command.getAncestorCategoryId());

        validateCategoryNameUniqueness(command.getCategoryName(), command.getAncestorCategoryId());

        category.setName(command.getCategoryName());
        category.setIcon(command.getIcon());
        category.setDescription(command.getDescription());
        Category update = categoryService.update(category);

        categoryHierarchyApplicationService.updatedCategoryHierarchy(UpdatedCategoryHierarchCommand.builder()
                .ancestorCategoryId(command.getAncestorCategoryId())
                .categoryId(update.getId())
                .sortOrder(command.getSortOrder()).build());

        CategoryDto currentCategory = customizedCategoryJpaDao.getCategories(CategoryQuery.builder()
                .status(CategoryStatus.ACTIVE)
                .categoryId(categoryId)
                .depth(1)
                .build()).getFirst();

        businessEventService.dispatch(CategoryUpdatedPayloadDto.builder().categoryId(update.getId()).previous(previousCategory).current(currentCategory).build());
    }

    @Override
    public Map<Integer, String> getCategoryTreeByLeafCategoryId(UUID leafCategoryId) {
        List<CategoryDto> categories = customizedCategoryJpaDao.getCategories(CategoryQuery.builder()
            .categoryId(leafCategoryId)
            .status(CategoryStatus.ACTIVE)
            .build());

        Map<Integer, String> depthToNameMap = new HashMap<>();

        // Add the leaf category (depth 0)
        Category leafCategory = categoryService.findById(leafCategoryId);
        if (leafCategory != null) {
            depthToNameMap.put(DEPTH_TO_CLASS, leafCategory.getName());
        }

        // Add ancestor categories by depth
        categories.stream()
            .filter(c -> c.getDepth() > DEPTH_TO_CLASS)
            .forEach(c -> depthToNameMap.put(c.getDepth(), c.getAncestorName()));

        return depthToNameMap;
    }

    @Override
    public Map<Integer, List<String>> getCategoryTreeByLeafCategoryName(String leafCategoryName) {
        if (leafCategoryName == null || leafCategoryName.trim().isEmpty()) {
            return Collections.emptyMap();
        }

        // Use the new method to get categories by leaf name
        List<CategoryDto> categories = customizedCategoryJpaDao.getCategories(CategoryQuery.builder()
            .leafCategoryName(leafCategoryName)
            .status(CategoryStatus.ACTIVE)
            .build());

        // Group categories by depth and collect ancestor names
        Map<Integer, List<String>> depthToNameMap = new HashMap<>();

        // Process leaf categories (depth 0)
        List<String> leafCategories = categories.stream()
            .map(CategoryDto::getCategoryName)
            .distinct()
            .toList();

        if (!leafCategories.isEmpty()) {
            depthToNameMap.put(DEPTH_TO_CLASS, leafCategories);
        }

        // Process ancestor categories by depth
        depthToNameMap.putAll(categories.stream()
            .filter(c -> c.getDepth() > DEPTH_TO_CLASS)
            .collect(Collectors.groupingBy(
                CategoryDto::getDepth,
                Collectors.mapping(CategoryDto::getAncestorName,
                    Collectors.collectingAndThen(Collectors.toSet(), ArrayList::new))
            )));

        return depthToNameMap;
    }


    private List<CategoryTreeDto> buildCategoryTree(List<CategoryDto> categoryDtos) {
        if (categoryDtos == null || categoryDtos.isEmpty()) {
            return Collections.emptyList();
        }

        // 1. Firstly, obtain the item statistics of all leaf nodes.
        Map<UUID, ItemCountsDto> leafItemCounts = getItemCountsByCategories(
            categoryDtos.stream()
                .filter(c -> !hasChildren(categoryDtos, c.getCategoryId()))
                .map(CategoryDto::getCategoryId)
                .toList()
        );

        // 2. Building a Basic Tree Structure
        Map<UUID, CategoryTreeDto> nodeMap = categoryDtos.stream()
            .collect(Collectors.toMap(
                CategoryDto::getCategoryId,
                dto -> {
                    CategoryTreeDto treeDto = convertToTreeDto(dto);
                    // If it is a leaf node, set the statistics directly.
                    ItemCountsDto counts = leafItemCounts.get(dto.getCategoryId());
                    if (counts != null) {
                        treeDto.setItemCountsDto(counts);
                    }
                    return treeDto;
                },
                (existing, replacement) -> existing.getDepth() <= replacement.getDepth() ? existing : replacement
            ));

        // 3. Establish parent-child relationship
        categoryDtos.stream()
            .filter(c -> c.getDepth() == 1)
            .collect(Collectors.groupingBy(CategoryDto::getAncestorCategoryId))
            .forEach((parentId, children) -> {
                CategoryTreeDto parent = nodeMap.get(parentId);
                if (parent != null) {
                    children.stream()
                        .map(c -> nodeMap.get(c.getCategoryId()))
                        .filter(Objects::nonNull)
                        .filter(child -> !parent.getChildCategories().contains(child))
                        .forEach(parent.getChildCategories()::add);
                }
            });

        // 4. Sort by sortOrder
        nodeMap.values().forEach(node ->
            node.getChildCategories().sort(Comparator.comparingInt(CategoryTreeDto::getSortOrder))
        );

        // 5. Calculate the statistics of each node from the bottom up
        List<CategoryTreeDto> rootNodes = categoryDtos.stream()
            .filter(c -> c.getDepth() == 0)
            .map(CategoryDto::getCategoryId)
            .distinct()
            .map(nodeMap::get)
            .filter(Objects::nonNull)
            .toList();

        // Calculate the statistical data of non-leaf nodes
        rootNodes.forEach(this::calculateNodeCounts);

        return rootNodes;
    }

    private CategoryTreeDto convertToTreeDto(CategoryDto category) {
        return CategoryTreeDto.builder()
            .categoryId(category.getCategoryId())
            .categoryName(category.getCategoryName())
            .sortOrder(category.getSortOrder())
            .depth(category.getDepth())
            .childCategories(new ArrayList<>())
            .itemCountsDto(new ItemCountsDto(0L, 0L, 0L))  // Initialize with zeros
            .build();
    }

    private void checkAncestorCategoryIdValid(UUID ancestorCategoryId) {
        if (null != ancestorCategoryId) {
            Category byId = categoryService.findById(ancestorCategoryId);
            if (byId == null) {
                log.info("[createCategory] Ancestor category not found, ancestorCategoryId: {}.", ancestorCategoryId);
                throw new ImsBusinessException(CATEGORY_NAME_ALREADY_EXISTS);
            }
        }
    }

    private void validateCategoryNameUniqueness(String categoryName, UUID ancestorCategoryId) {
        boolean categoryExists = checkIfCategoryNameExists(categoryName, ancestorCategoryId);
        if (categoryExists) {
            log.info("[validateCategoryNameUniqueness] Category name already exists, category name: {}.", categoryName);
            throw new ImsBusinessException(CATEGORY_NAME_ALREADY_EXISTS);
        }
    }

    private boolean checkIfCategoryNameExists(String categoryName, UUID ancestorCategoryId) {
        int depth = (ancestorCategoryId == null) ? 0 : 1;

        List<CategoryDto> categories = customizedCategoryJpaDao.getCategories(CategoryQuery.builder()
            .status(CategoryStatus.ACTIVE)
            .ancestorCategoryId(ancestorCategoryId)
            .depth(depth)
            .build());

        return categories.stream()
            .anyMatch(category -> category.getCategoryName().equals(categoryName));
    }

    @Override
    public List<CategoryDto> searchCategoriesByName(String name) {
        if (StringUtils.isBlank(name)) {
            return Collections.emptyList();
        }

        return categoryService.findByNameAndStatus(name, CategoryStatus.ACTIVE).stream()
            .map(this::convertToDto)
            .toList();
    }


    private CategoryDto convertToDto(Category category) {
        return CategoryDto.builder()
            .categoryId(category.getId())
            .categoryName(category.getName())
            .build();
    }

    /**
     * Get a mapping of all leaf nodes and their ancestors
     *
     * @return Map<UUID, Map < Integer, String>> where the key is the leaf node ID, and the value is a map containing depths and
     * category names
     */
    @Override
    public Map<UUID, Map<Integer, String>> getAllLeafNodesWithAncestors() {
        // Get all leaf nodes with depth = DEPTH_TO_GREAT_GRANDPARENT
        List<CategoryHierarchy> allLeafNodes = categoryHierarchyService.findByDepth(DEPTH_TO_GREAT_GRANDPARENT);

        // Early return if no leaf nodes found
        if (allLeafNodes.isEmpty()) {
            return Collections.emptyMap();
        }

        // Extract unique leaf category IDs
        List<UUID> leafNodeCategoryIds = allLeafNodes.stream()
            .map(CategoryHierarchy::getCategoryId)
            .distinct()
            .toList();

        // Fetch all relevant categories in a single query
        List<CategoryDto> allCategories = customizedCategoryJpaDao.getCategories(CategoryQuery.builder()
            .status(CategoryStatus.ACTIVE)
            .categoryIds(leafNodeCategoryIds)
            .build());

        // Early return if no categories found
        if (allCategories.isEmpty()) {
            return Collections.emptyMap();
        }

        // Build the result mapping more efficiently
        Map<UUID, Map<Integer, String>> result = new HashMap<>();

        // Pre-process categories by leaf ID for faster lookup
        Map<UUID, List<CategoryDto>> categoriesByLeafId = allCategories.stream()
            .collect(Collectors.groupingBy(
                CategoryDto::getCategoryId,
                Collectors.toList()
            ));

        // Process each leaf node
        for (UUID leafId : leafNodeCategoryIds) {
            List<CategoryDto> leafCategories = categoriesByLeafId.get(leafId);

            // Skip if no categories found for this leaf ID
            if (leafCategories == null || leafCategories.isEmpty()) {
                continue;
            }

            // Create depth to name mapping with initial capacity
            Map<Integer, String> depthToNameMap = new LinkedHashMap<>();

            // Add leaf node name (first category in the list)
            depthToNameMap.put(0, leafCategories.getFirst().getCategoryName());

            // Add all ancestors more efficiently
            leafCategories.forEach(cat -> {
                if (cat.getDepth() > 0) {
                    depthToNameMap.put(cat.getDepth(), cat.getAncestorName());
                }
            });

            // Add to result
            result.put(leafId, depthToNameMap);
        }

        return result;
    }

    @Override
    public void deleteCategory(UUID categoryId) {
        log.info("[deleteCategory] param, categoryId:{}.", categoryId);

        // Validate category exists
        Category category = categoryService.findById(categoryId);
        if (category == null) {
            log.error("[deleteCategory] Category not found, categoryId:{}.", categoryId);
            throw new ImsBusinessException(CATEGORY_NOT_FOUND);
        }

        // Get hierarchies and determine leaf categories
        List<CategoryHierarchy> hierarchies = categoryHierarchyService.findByAncestorCategoryId(categoryId);
        
        final List<UUID> leafCategoryIds;
        final List<UUID> allCategoryIdsToDelete = new ArrayList<>();
        allCategoryIdsToDelete.add(categoryId);
        
        if (hierarchies.isEmpty()) {
            leafCategoryIds = Collections.singletonList(categoryId);
        } else {
            // Find leaf categories (those with max depth)
            int maxDepth = hierarchies.stream().mapToInt(CategoryHierarchy::getDepth).max().getAsInt();
            
            leafCategoryIds = hierarchies.stream()
                .filter(h -> h.getDepth() == maxDepth)
                .map(CategoryHierarchy::getCategoryId)
                .toList();
                
            // Add all descendants to deletion list
            hierarchies.stream()
                .map(CategoryHierarchy::getCategoryId)
                .distinct()
                .forEach(allCategoryIdsToDelete::add);
        }

        // Verify no items exist in leaf categories
        verifyNoItemsInCategories(leafCategoryIds, categoryId);

        // Delete categories and their hierarchies
        allCategoryIdsToDelete.forEach(categoryService::delete);
        
        // Delete Category Hierarchy
        deleteCategoryHierarchy(allCategoryIdsToDelete);
    }

    private void verifyNoItemsInCategories(List<UUID> categoryIds, UUID originalCategoryId) {
        if (categoryIds.isEmpty()) {
            return;
        }
        
        List<CategoryItemCountsDto> itemCounts = 
            itemSearchApplicationService.countItemsByCategoryIdAndStatus(categoryIds);
        
        boolean hasItems = itemCounts.stream()
            .anyMatch(dto -> hasNonZeroCount(dto.getActiveCount()) || 
                             hasNonZeroCount(dto.getDraftCount()) || 
                             hasNonZeroCount(dto.getArchivedCount()));
        
        if (hasItems) {
            log.error("[deleteCategory] Cannot delete category with items, categoryId:{}.", originalCategoryId);
            throw new ImsBusinessException(CATEGORY_HAS_ITEMS);
        }
    }

    private boolean hasNonZeroCount(Long count) {
        return count != null && count > 0;
    }

    private void deleteCategoryHierarchy(List<UUID> categoryIdsToDelete) {
        List<CategoryHierarchy> hierarchiesToDelete = 
            categoryHierarchyService.findByCategoryIdIn(categoryIdsToDelete);
        
        List<UUID> hierarchyIds = hierarchiesToDelete.stream()
            .map(CategoryHierarchy::getId)
            .toList();

        if (!hierarchyIds.isEmpty()) {
            hierarchyIds.forEach(categoryHierarchyApplicationService::deletedCategoryHierarchy);
        }
    }

    // Determine if there are child nodes
    private boolean hasChildren(List<CategoryDto> categoryDtos, UUID categoryId) {
        return categoryDtos.stream()
            .anyMatch(dto -> Objects.equals(dto.getAncestorCategoryId(), categoryId));
    }

    // Retrieve statistical data of leaf nodes from the database
    private Map<UUID, ItemCountsDto> getItemCountsByCategories(List<UUID> categoryIds) {
        if (categoryIds == null || categoryIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // Convert CategoryItemCountsDto to ItemCountsDto directly
        return itemSearchApplicationService.countItemsByCategoryIdAndStatus(categoryIds).stream()
            .collect(Collectors.toMap(
                CategoryItemCountsDto::getCategoryId,
                dto -> ItemCountsDto.builder()
                    .activeCount(dto.getActiveCount())
                    .draftCount(dto.getDraftCount())
                    .archivedCount(dto.getArchivedCount())
                    .build()
            ));
    }

    // Recursive calculation of node statistics
    private void calculateNodeCounts(CategoryTreeDto node) {
        if (node.getChildCategories().isEmpty()) {
            return; // Leaf node statistics are already set
        }

        // Recursively process all child nodes
        node.getChildCategories().forEach(this::calculateNodeCounts);

        // Sum up child node statistics
        long activeCount = 0;
        long draftCount = 0;
        long archivedCount = 0;

        for (CategoryTreeDto child : node.getChildCategories()) {
            ItemCountsDto childCounts = child.getItemCountsDto();
            if (childCounts != null) {
                activeCount += childCounts.getActiveCount() != null ? childCounts.getActiveCount() : 0;
                draftCount += childCounts.getDraftCount() != null ? childCounts.getDraftCount() : 0;
                archivedCount += childCounts.getArchivedCount() != null ? childCounts.getArchivedCount() : 0;
            }
        }

        // Update node's counts
        ItemCountsDto nodeCounts = node.getItemCountsDto();
        if (nodeCounts == null) {
            nodeCounts = new ItemCountsDto(activeCount, draftCount, archivedCount);
            node.setItemCountsDto(nodeCounts);
        } else {
            nodeCounts.setActiveCount(activeCount);
            nodeCounts.setDraftCount(draftCount);
            nodeCounts.setArchivedCount(archivedCount);
        }
    }

}
