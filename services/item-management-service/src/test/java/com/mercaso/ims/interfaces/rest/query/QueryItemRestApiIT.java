package com.mercaso.ims.interfaces.rest.query;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.dto.ItemCategoryDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ItemPackingInfoDto;
import com.mercaso.ims.application.dto.QueryItemCategoryListDto;
import com.mercaso.ims.application.query.ItemCategoryQuery;
import com.mercaso.ims.domain.item.enums.ItemUpcType;
import com.mercaso.ims.infrastructure.repository.item.jpa.dataobject.ItemDo;
import com.mercaso.ims.infrastructure.repository.itemupc.jpa.dataobject.ItemUPCDo;
import com.mercaso.ims.utils.itemupc.ItemUPCUtil;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

@Slf4j
class QueryItemRestApiIT extends AbstractIT {

    @Test
    void shouldSuccessWhenFindItemsByIdIn() throws Exception {
        String firstSkuNumber = RandomStringUtils.randomAlphabetic(10);
        String secondSkuNumber = RandomStringUtils.randomAlphabetic(10);
        String department = "testDepartmentForFindItemsByIdIn";
        String category = "testCategoryForFindItemsByIdIn";
        String subCategory = "testSubCategoryForFindItemsByIdIn";
        String clazz = "testClazzForFindItemsByIdIn";
        String brandName = "testBrandNameForFindItemsByIdIn";
        String vendorName = "testVendorNameForFindItemsByIdIn";

        String vendorItemNumber = RandomStringUtils.randomAlphabetic(10);

        ItemDo firstItemDo = buildItemData(firstSkuNumber,
            department,
            category,
            subCategory,
            clazz,
            brandName,
            vendorName,
            vendorItemNumber);
        ItemDo secondStemDo = buildItemData(secondSkuNumber,
            department,
            category,
            subCategory,
            clazz,
            brandName,
            vendorName,
            vendorItemNumber);

        List<UUID> ids = Arrays.asList(firstItemDo.getId(), secondStemDo.getId());
        List<String> skus = Arrays.asList(firstSkuNumber, secondSkuNumber);

        List<ItemCategoryDto> result = queryItemRestApiUtil.findItemsByIdIn(ids);

        Assertions.assertEquals(2, result.size());
        result.forEach(itemCategoryDto -> {
            Assertions.assertTrue(ids.contains(itemCategoryDto.getId()));
            Assertions.assertTrue(skus.contains(itemCategoryDto.getSkuNumber()));
            Assertions.assertEquals(brandName, itemCategoryDto.getBrand());
            Assertions.assertNotNull(itemCategoryDto.getVersionNumber());

        });


    }

    @Test
    void shouldSuccessWhenFindItemsBySkuIn() throws Exception {
        String firstSkuNumber = RandomStringUtils.randomAlphabetic(10);
        String secondSkuNumber = RandomStringUtils.randomAlphabetic(10);
        String department = "testDepartmentForFindItemsBySkuIn";
        String category = "testCategoryForFindItemsBySkuIn";
        String subCategory = "testSubCategoryForFindItemsBySkuIn";
        String clazz = "testClazzForFindItemsBySkuIn";
        String brandName = "testBrandNameForFindItemsBySkuIn";
        String vendorName = "testVendorNameForFindItemsBySkuIn";

        String vendorItemNumber = RandomStringUtils.randomAlphabetic(10);

        ItemDo firstItemDo = buildItemData(firstSkuNumber,
            department,
            category,
            subCategory,
            clazz,
            brandName,
            vendorName,
            vendorItemNumber);
        ItemDo secondStemDo = buildItemData(secondSkuNumber,
            department,
            category,
            subCategory,
            clazz,
            brandName,
            vendorName,
            vendorItemNumber);

        List<UUID> ids = Arrays.asList(firstItemDo.getId(), secondStemDo.getId());
        List<String> skus = Arrays.asList(firstSkuNumber, secondSkuNumber);

        List<ItemCategoryDto> result = queryItemRestApiUtil.findItemsBySkuIn(skus);

        Assertions.assertEquals(2, result.size());
        result.forEach(itemCategoryDto -> {
            Assertions.assertTrue(ids.contains(itemCategoryDto.getId()));
            Assertions.assertTrue(skus.contains(itemCategoryDto.getSkuNumber()));
            Assertions.assertEquals(brandName, itemCategoryDto.getBrand());
            Assertions.assertNotNull(itemCategoryDto.getVersionNumber());
        });

    }

    @Test
    void shouldSuccessWhenFindItemPackingInfosByIdIn() throws Exception {
        String firstSkuNumber = RandomStringUtils.randomAlphabetic(10);
        String secondSkuNumber = RandomStringUtils.randomAlphabetic(10);

        String firstUPCNumber = RandomStringUtils.randomAlphabetic(10);
        String secondUPCNumber = RandomStringUtils.randomAlphabetic(10);
        String thridUPCNumber = RandomStringUtils.randomAlphabetic(10);
        String forthUPCNumber = RandomStringUtils.randomAlphabetic(10);

        ItemUPCDo firstUPC = ItemUPCUtil.buildItemUPCDo(firstUPCNumber, ItemUpcType.EACH_UPC);
        ItemUPCDo secondUPC = ItemUPCUtil.buildItemUPCDo(secondUPCNumber, ItemUpcType.VARIANT_BARCODE);
        ItemUPCDo thridUPC = ItemUPCUtil.buildItemUPCDo(thridUPCNumber, ItemUpcType.MASTER_UPC);
        ItemUPCDo forthUPC = ItemUPCUtil.buildItemUPCDo(forthUPCNumber, ItemUpcType.EACH_UPC);

        List<ItemUPCDo> firstUpcDos = Arrays.asList(firstUPC, secondUPC);
        List<ItemUPCDo> secondUpcDos = Arrays.asList(thridUPC, forthUPC);

        ItemDo firstItemDo = buildItemDataWithUpc(firstSkuNumber, firstUpcDos);
        ItemDo secondStemDo = buildItemDataWithUpc(secondSkuNumber, secondUpcDos);

        List<UUID> ids = Arrays.asList(firstItemDo.getId(), secondStemDo.getId());
        List<String> skus = Arrays.asList(firstSkuNumber, secondSkuNumber);

        List<ItemPackingInfoDto> result = queryItemRestApiUtil.findItemsPackingInfoByIdIn(ids);

        Assertions.assertEquals(2, result.size());
        result.forEach(itemCategoryDto -> {
            Assertions.assertTrue(ids.contains(itemCategoryDto.getId()));
            Assertions.assertTrue(skus.contains(itemCategoryDto.getSkuNumber()));
        });


    }


    @Test
    void shouldSuccessWhenFindItemsBySku() throws Exception {
        String firstSkuNumber = RandomStringUtils.randomAlphabetic(10);
        String department = "testDepartmentForFindItemsBySkuIn";
        String category = "testCategoryForFindItemsBySkuIn";
        String subCategory = "testSubCategoryForFindItemsBySkuIn";
        String clazz = "testClazzForFindItemsBySkuIn";
        String brandName = "testBrandNameForFindItemsBySkuIn";
        String vendorName = "testVendorNameForFindItemsBySkuIn";

        String vendorItemNumber = RandomStringUtils.randomAlphabetic(10);

        ItemDo firstItemDo = buildItemData(firstSkuNumber,
            department,
            category,
            subCategory,
            clazz,
            brandName,
            vendorName,
            vendorItemNumber);

        ItemDto itemDto = queryItemRestApiUtil.itemDetailRequest(firstSkuNumber);

        Assertions.assertEquals(firstItemDo.getId(), itemDto.getId());
        Assertions.assertEquals(firstSkuNumber, itemDto.getSkuNumber());
        Assertions.assertEquals(department, itemDto.getDepartment());
        Assertions.assertEquals(category, itemDto.getCategory());
        Assertions.assertEquals(subCategory, itemDto.getSubCategory());

    }


    @Test
    void shouldSuccessWhenQueryItem() throws Exception {
        String firstSkuNumber = RandomStringUtils.randomAlphabetic(10);
        String secondSkuNumber = RandomStringUtils.randomAlphabetic(10);
        String department = "testDepartmentForQuery";
        String category = "testCategoryForForQuery";
        String subCategory = "testSubCategoryForQuery";
        String clazz = "testClazzForForQuery";
        String brandName = "testBrandNameFForQuery";
        String vendorName = "testVendorNameForQuery";

        String vendorItemNumber = RandomStringUtils.randomAlphabetic(10);

        ItemDo firstItemDo = buildItemData(firstSkuNumber,
            department,
            category,
            subCategory,
            clazz,
            brandName,
            vendorName,
            vendorItemNumber);
        ItemDo secondStemDo = buildItemData(secondSkuNumber,
            department,
            category,
            subCategory,
            clazz,
            brandName,
            vendorName,
            vendorItemNumber);

        List<UUID> ids = Arrays.asList(firstItemDo.getId(), secondStemDo.getId());
        List<String> skus = Arrays.asList(firstSkuNumber, secondSkuNumber);

        QueryItemCategoryListDto result1 = queryItemRestApiUtil.queryItemCategoryList(ItemCategoryQuery.builder()
            .ids(ids)
            .build());
        List<ItemCategoryDto> itemCategoryDtoListByIds = result1.getData();
        Assertions.assertEquals(2, itemCategoryDtoListByIds.size());
        itemCategoryDtoListByIds.forEach(itemCategoryDto -> {
            Assertions.assertTrue(ids.contains(itemCategoryDto.getId()));
            Assertions.assertTrue(skus.contains(itemCategoryDto.getSkuNumber()));
            Assertions.assertEquals(brandName, itemCategoryDto.getBrand());
        });

        QueryItemCategoryListDto result2 = queryItemRestApiUtil.queryItemCategoryList(ItemCategoryQuery.builder()
            .skus(skus)
            .build());
        List<ItemCategoryDto> itemCategoryDtoListBySkus = result2.getData();
        Assertions.assertEquals(2, itemCategoryDtoListBySkus.size());
        itemCategoryDtoListBySkus.forEach(itemCategoryDto -> {
            Assertions.assertTrue(ids.contains(itemCategoryDto.getId()));
            Assertions.assertTrue(skus.contains(itemCategoryDto.getSkuNumber()));
            Assertions.assertEquals(brandName, itemCategoryDto.getBrand());
        });

    }


    @Test
    void shouldSuccessWhenFindItemsByIdAndVersion() throws Exception {
        String firstSkuNumber = RandomStringUtils.randomAlphabetic(10);
        String department = "testDepartmentForFindItemsByVersion-version";
        String category = "testCategoryForFindItemsByVersion-version";
        String subCategory = "testSubCategoryForFindItemsByVersion-version";
        String clazz = "testClazzForFindItemsByVersion-version";
        String brandName = "testBrandNameForFindItemsByVersion-version";
        String vendorName = "testVendorNameForFindItemsByVersion-version";

        String vendorItemNumber = RandomStringUtils.randomAlphabetic(10);

        ItemDo firstItemDo = buildItemData(firstSkuNumber,
            department,
            category,
            subCategory,
            clazz,
            brandName,
            vendorName,
            vendorItemNumber);

        ItemCategoryDto itemCategoryDto = queryItemRestApiUtil.findByIdAndVersion(firstItemDo.getId(), 1);

        Assertions.assertEquals(1, itemCategoryDto.getVersionNumber());
    }

    @Test
    void shouldSuccessWhenGetItemsByUpc() throws Exception {
        // Given
        String firstSkuNumber = RandomStringUtils.randomAlphabetic(10);
        String secondSkuNumber = RandomStringUtils.randomAlphabetic(10);
        String upcNumber = RandomStringUtils.randomNumeric(12); // UPC is typically 12 digits
        String department = "testDepartmentForGetItemsByUpc";
        String category = "testCategoryForGetItemsByUpc";
        String subCategory = "testSubCategoryForGetItemsByUpc";
        String clazz = "testClazzForGetItemsByUpc";
        String brandName = "testBrandNameForGetItemsByUpc";
        String vendorName = "testVendorNameForGetItemsByUpc";
        String vendorItemNumber = RandomStringUtils.randomAlphabetic(10);

        // Create UPC data objects
        ItemUPCDo firstUPC = ItemUPCUtil.buildItemUPCDo(upcNumber, ItemUpcType.EACH_UPC);
        ItemUPCDo secondUPC = ItemUPCUtil.buildItemUPCDo(upcNumber, ItemUpcType.CASE_UPC);

        List<ItemUPCDo> firstUpcDos = List.of(firstUPC);
        List<ItemUPCDo> secondUpcDos = List.of(secondUPC);

        // Create items with the same UPC number
        ItemDo firstItemDo = buildItemData(firstSkuNumber,
            department,
            category,
            subCategory,
            clazz,
            brandName,
            vendorName,
            vendorItemNumber,
            firstUpcDos);
        ItemDo secondItemDo = buildItemData(secondSkuNumber,
            department,
            category,
            subCategory,
            clazz,
            brandName,
            vendorName,
            vendorItemNumber,
            secondUpcDos);

        // When
        List<ItemDto> result = queryItemRestApiUtil.getItemsByUpc(upcNumber);

        // Then
        Assertions.assertNotNull(result);
        Assertions.assertEquals(2, result.size());

        List<UUID> expectedIds = Arrays.asList(firstItemDo.getId(), secondItemDo.getId());
        List<String> expectedSkus = Arrays.asList(firstSkuNumber, secondSkuNumber);

        result.forEach(itemDto -> {
            Assertions.assertTrue(expectedIds.contains(itemDto.getId()));
            Assertions.assertTrue(expectedSkus.contains(itemDto.getSkuNumber()));
            Assertions.assertEquals(department, itemDto.getDepartment());
            Assertions.assertEquals(category, itemDto.getCategory());
            Assertions.assertEquals(subCategory, itemDto.getSubCategory());
            Assertions.assertEquals(clazz, itemDto.getClazz());

            // Verify that the item has the expected UPC
            boolean hasExpectedUpc = itemDto.getItemUPCs().stream()
                .anyMatch(upc -> upcNumber.equals(upc.getUpcNumber()));
            Assertions.assertTrue(hasExpectedUpc, "Item should have the expected UPC number");
        });
    }

}