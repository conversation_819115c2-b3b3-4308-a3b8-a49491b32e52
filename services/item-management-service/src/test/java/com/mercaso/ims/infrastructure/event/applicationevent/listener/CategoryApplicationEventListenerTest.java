package com.mercaso.ims.infrastructure.event.applicationevent.listener;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;

import com.mercaso.ims.application.command.CreateCategoryHierarchCommand;
import com.mercaso.ims.application.dto.CategoryDto;
import com.mercaso.ims.application.dto.event.CategoryCreatedApplicationEvent;
import com.mercaso.ims.application.dto.event.CategoryDeletedApplicationEvent;
import com.mercaso.ims.application.dto.event.CategoryUpdatedApplicationEvent;
import com.mercaso.ims.application.dto.payload.CategoryCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.CategoryDeletedDto;
import com.mercaso.ims.application.dto.payload.CategoryDeletedPayloadDto;
import com.mercaso.ims.application.dto.payload.CategoryUpdatedPayloadDto;
import com.mercaso.ims.application.queryservice.CategoryQueryApplicationService;
import com.mercaso.ims.application.service.CategoryHierarchyApplicationService;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.Executor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class CategoryApplicationEventListenerTest {

    @Mock
    private CategoryHierarchyApplicationService categoryHierarchyApplicationService;

    @InjectMocks
    private CategoryApplicationEventListener categoryApplicationEventListener;

    @Mock
    private CategoryQueryApplicationService categoryQueryApplicationService;

    @Mock
    private Executor taskExecutor;


    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void should_create_category_hierarchy_when_handling_category_created_event() {
        // Given
        UUID categoryId = UUID.randomUUID();
        UUID ancestorCategoryId = UUID.randomUUID();
        int sortOrder = 1;

        CategoryDto categoryDto = CategoryDto.builder()
            .ancestorCategoryId(ancestorCategoryId)
            .sortOrder(sortOrder)
            .build();

        CategoryCreatedPayloadDto payloadDto = CategoryCreatedPayloadDto.builder()
            .categoryId(categoryId)
            .data(categoryDto)
            .build();

        CategoryCreatedApplicationEvent event = new CategoryCreatedApplicationEvent("Category", payloadDto);

        // When
        categoryApplicationEventListener.handleCategoryCreatedApplicationEvent(event);

        // Then
        verify(categoryHierarchyApplicationService).createCategoryHierarchy(any());
    }

    @Test
    void should_handle_null_ancestor_category_id() {
        // Given
        UUID categoryId = UUID.randomUUID();
        int sortOrder = 1;

        CategoryDto categoryDto = CategoryDto.builder()
            .ancestorCategoryId(null)
            .sortOrder(sortOrder)
            .build();

        CategoryCreatedPayloadDto payloadDto = CategoryCreatedPayloadDto.builder()
            .categoryId(categoryId)
            .data(categoryDto)
            .build();

        CategoryCreatedApplicationEvent event = new CategoryCreatedApplicationEvent("Category", payloadDto);

        // When
        categoryApplicationEventListener.handleCategoryCreatedApplicationEvent(event);

        // Then
        verify(categoryHierarchyApplicationService).createCategoryHierarchy(any());
    }

    @Test
    void should_handle_null_sort_order() {
        // Given
        UUID categoryId = UUID.randomUUID();
        UUID ancestorCategoryId = UUID.randomUUID();

        CategoryDto categoryDto = CategoryDto.builder()
            .ancestorCategoryId(ancestorCategoryId)
            .sortOrder(null)
            .build();

        CategoryCreatedPayloadDto payloadDto = CategoryCreatedPayloadDto.builder()
            .categoryId(categoryId)
            .data(categoryDto)
            .build();

        CategoryCreatedApplicationEvent event = new CategoryCreatedApplicationEvent("Category", payloadDto);

        // When
        categoryApplicationEventListener.handleCategoryCreatedApplicationEvent(event);

        // Then
        verify(categoryHierarchyApplicationService).createCategoryHierarchy(any());
    }

    @Test
    void should_log_event_payload() {
        // Given
        UUID categoryId = UUID.randomUUID();
        CategoryDto categoryDto = CategoryDto.builder().build();
        CategoryCreatedPayloadDto payloadDto = CategoryCreatedPayloadDto.builder()
            .categoryId(categoryId)
            .data(categoryDto)
            .build();
        CategoryCreatedApplicationEvent event = new CategoryCreatedApplicationEvent("Category", payloadDto);

        // When
        categoryApplicationEventListener.handleCategoryCreatedApplicationEvent(event);

        // Then
        // Note: Since we can't easily verify log output in unit tests,
        // this test mainly ensures the logging doesn't throw any exceptions
        verify(categoryHierarchyApplicationService).createCategoryHierarchy(any(CreateCategoryHierarchCommand.class));
    }


    @Test
    void should_update_category_hierarchy_when_handling_category_updated_event() {
        // Given
        UUID categoryId = UUID.randomUUID();
        UUID ancestorCategoryId = UUID.randomUUID();
        int sortOrder = 1;

        CategoryDto categoryDto = CategoryDto.builder()
            .ancestorCategoryId(ancestorCategoryId)
            .sortOrder(sortOrder)
            .build();

        CategoryUpdatedPayloadDto payloadDto = CategoryUpdatedPayloadDto.builder()
            .categoryId(categoryId)
            .current(categoryDto)
            .build();

        CategoryUpdatedApplicationEvent event = new CategoryUpdatedApplicationEvent("Category", payloadDto);

        // When
        categoryApplicationEventListener.handleCategoryUpdatedApplicationEvent(event);

        // Then
        verify(categoryHierarchyApplicationService).updatedCategoryHierarchy(any());
    }

    @Test
    void should_handle_null_ancestor_category_id_updated() {
        // Given
        UUID categoryId = UUID.randomUUID();
        int sortOrder = 1;

        CategoryDto categoryDto = CategoryDto.builder()
            .ancestorCategoryId(null)
            .sortOrder(sortOrder)
            .build();

        CategoryUpdatedPayloadDto payloadDto = CategoryUpdatedPayloadDto.builder()
            .categoryId(categoryId)
            .current(categoryDto)
            .build();

        CategoryUpdatedApplicationEvent event = new CategoryUpdatedApplicationEvent("Category", payloadDto);

        // When
        categoryApplicationEventListener.handleCategoryUpdatedApplicationEvent(event);

        // Then
        verify(categoryHierarchyApplicationService).updatedCategoryHierarchy(any());
    }

    @Test
    void should_delete_category_hierarchies_when_handling_category_deleted_event() {
        // Given
        UUID categoryId = UUID.randomUUID();
        UUID hierarchyId1 = UUID.randomUUID();
        UUID hierarchyId2 = UUID.randomUUID();
        List<UUID> categoryHierarchyIds = Arrays.asList(hierarchyId1, hierarchyId2);

        CategoryDeletedDto categoryDeletedDto = CategoryDeletedDto.builder()
            .categoryHierarchyIds(categoryHierarchyIds)
            .build();

        CategoryDeletedPayloadDto payloadDto = CategoryDeletedPayloadDto.builder()
            .categoryId(categoryId)
            .data(categoryDeletedDto)
            .build();

        CategoryDeletedApplicationEvent event = new CategoryDeletedApplicationEvent("Category", payloadDto);

        // When
        categoryApplicationEventListener.handleCategoryDeletedApplicationEvent(event);

        // Then
        verify(categoryHierarchyApplicationService).deletedCategoryHierarchy(hierarchyId1);
        verify(categoryHierarchyApplicationService).deletedCategoryHierarchy(hierarchyId2);
        verifyNoMoreInteractions(categoryHierarchyApplicationService);
    }

    @Test
    void should_not_delete_any_hierarchies_when_list_is_empty() {
        // Given
        UUID categoryId = UUID.randomUUID();
        List<UUID> categoryHierarchyIds = Collections.emptyList();

        CategoryDeletedDto categoryDeletedDto = CategoryDeletedDto.builder()
            .categoryHierarchyIds(categoryHierarchyIds)
            .build();

        CategoryDeletedPayloadDto payloadDto = CategoryDeletedPayloadDto.builder()
            .categoryId(categoryId)
            .data(categoryDeletedDto)
            .build();

        CategoryDeletedApplicationEvent event = new CategoryDeletedApplicationEvent("Category", payloadDto);

        // When
        categoryApplicationEventListener.handleCategoryDeletedApplicationEvent(event);

        // Then
        verifyNoInteractions(categoryHierarchyApplicationService);
    }

    @Test
    void should_log_event_payload_when_handling_deleted_event() {
        // Given
        UUID categoryId = UUID.randomUUID();
        UUID hierarchyId = UUID.randomUUID();
        List<UUID> categoryHierarchyIds = Collections.singletonList(hierarchyId);

        CategoryDeletedDto categoryDeletedDto = CategoryDeletedDto.builder()
            .categoryHierarchyIds(categoryHierarchyIds)
            .build();

        CategoryDeletedPayloadDto payloadDto = CategoryDeletedPayloadDto.builder()
            .categoryId(categoryId)
            .data(categoryDeletedDto)
            .build();

        CategoryDeletedApplicationEvent event = new CategoryDeletedApplicationEvent("Category", payloadDto);

        // When
        categoryApplicationEventListener.handleCategoryDeletedApplicationEvent(event);

        // Then
        // Note: Since we can't easily verify log output in unit tests,
        // this test mainly ensures the logging doesn't throw any exceptions
        verify(categoryHierarchyApplicationService).deletedCategoryHierarchy(hierarchyId);
    }
}