package com.mercaso.data.metrics.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.time.LocalDate;
import lombok.Data;

@Entity
@Data
@Table(name = "metrics_territory_declining_order_frequency")
public class MetricsTerritoryDecliningOrderFrequencyEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "address_id", nullable = false)
    private String addressId;

    @Column(name = "decrease_pct", nullable = false)
    private Double decreasePct;

    @Column(name = "has_alert", nullable = false)
    private Boolean hasAlert;

    @Column(name = "alert_date", nullable = false)
    private LocalDate alertDate;

}
