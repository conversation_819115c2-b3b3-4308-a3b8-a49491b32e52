package com.mercaso.data.third_party.mapper;

import com.mercaso.data.third_party.entity.shopify.product.ShopifyProductEntity;
import com.mercaso.data.third_party.dto.shopify.ShopifyProductDto;
import com.mercaso.data.third_party.entity.shopify.product.ShopifyProductImageEntity;
import com.mercaso.data.third_party.entity.shopify.product.ShopifyProductVariantEntity;
import org.mapstruct.*;

@Mapper(componentModel = "spring")
public interface ShopifyProductMapper {

    @Mapping(target = "productId", source = "id")
    @Mapping(target = "variants", source = "variants")
    @Mapping(target = "images", source = "images")
    ShopifyProductDto toShopifyProductDto(ShopifyProductEntity entity);

    @Mapping(target = "id", source = "id")
    @Mapping(target = "sku", source = "sku")
    @Mapping(target = "position", source = "position")
    @Mapping(target = "title", source = "title")
    ShopifyProductDto.VariantDto toVariantDto(ShopifyProductVariantEntity variant);

    @Mapping(target = "id", source = "id")
    ShopifyProductDto.ImageDto toImageDto(ShopifyProductImageEntity image);
}
