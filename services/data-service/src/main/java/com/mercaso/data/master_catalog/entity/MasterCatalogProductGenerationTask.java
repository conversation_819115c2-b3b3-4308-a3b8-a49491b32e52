package com.mercaso.data.master_catalog.entity;

import com.mercaso.data.master_catalog.enums.ProductGenerationTaskStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/**
 * Entity referring to {@link com.mercaso.data.master_catalog.dto.MasterCatalogProductGenerationTaskDto}
 */
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "master_catalog_product_generation_task", schema = "public")
public class MasterCatalogProductGenerationTask extends BaseEntity {

    @Size(max = 256)
    @NotNull
    @Column(name = "name", nullable = false, length = 256)
    private String name;

    @NotNull
    @Column(name = "status", nullable = false)
    @Enumerated(EnumType.STRING)
    private ProductGenerationTaskStatus status;

    @Column(name = "completed_at")
    private Instant completedAt;

}