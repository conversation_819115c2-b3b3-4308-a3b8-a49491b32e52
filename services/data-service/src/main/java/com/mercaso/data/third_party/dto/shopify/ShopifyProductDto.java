package com.mercaso.data.third_party.dto.shopify;

import java.util.List;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@RequiredArgsConstructor
public class ShopifyProductDto {
    private String productId;
    private List<VariantDto> variants;
    private List<ImageDto> images;

    @Data
    public static class VariantDto {
        private String id;
        private String sku;
        private int position;
        private String title;
    }

    @Data
    public static class ImageDto {
        private String id;
    }
}
